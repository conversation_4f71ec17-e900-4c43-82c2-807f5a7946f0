import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEmail, IsNotEmpty, IsArray, IsUrl } from 'class-validator';

export class ImageUploadItemDto {
  @ApiProperty({
    description: 'ID of the rejected attachment to replace',
    example: 'uuid-of-rejected-attachment',
  })
  @IsString()
  @IsNotEmpty()
  rejectedAttachmentId: string;

  @ApiProperty({
    description: 'URL of the uploaded image',
    example: 'https://storage.example.com/new_pet_photo.jpg',
  })
  @IsUrl()
  @IsNotEmpty()
  imageUrl: string;
}

export class UploadImageRequestDto {
  @ApiProperty({
    description: 'Shopify order number',
    example: '12345',
  })
  @IsString()
  @IsNotEmpty()
  orderNumber: string;

  @ApiProperty({
    description: 'Customer email for validation',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'Array of image uploads with their corresponding rejected attachment IDs',
    type: [ImageUploadItemDto],
  })
  @IsArray()
  uploads: ImageUploadItemDto[];
} 