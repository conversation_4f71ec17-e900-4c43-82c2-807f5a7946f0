import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Order } from '../orders/entities/order.entity';
import { LineItem } from '../orders/entities/line-item.entity';
import { ProductSku } from '../product-sku/entities/product-sku.entity';
import { Attachment } from '../attachments/entities/attachment.entity';
import { EmailUtil } from '../utils/email.util';
import { accessEnv } from '../env.validation';
import { DBHelper } from '../helpers/db.helpers';

@Injectable()
export class OrderTrackingService {
  constructor(
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    @InjectRepository(Attachment)
    private readonly attachmentRepository: Repository<Attachment>,
  ) {}

  async getOrderDetails(
    orderNumber: string,
    customerEmail: string,
  ): Promise<any> {
    const order = await DBHelper.findOne(this.orderRepository, {
      where: {
        shopifyOrderNumber: orderNumber,
        customerEmail: customerEmail,
      },
      relations: ['lineItems'],
    });

    if (!order) {
      throw new HttpException(
        {
          statusCode: HttpStatus.NOT_FOUND,
          message: 'Order not found',
          error: 'Not Found',
        },
        HttpStatus.NOT_FOUND,
      );
    }

    const transformedOrder = {
      shopifyOrderNumber: order.shopifyOrderNumber,
      orderDate: order.orderDate,
      orderStatus: order.orderStatus,
      statusUpdatedAt: order.statusUpdatedAt,
      customerFirstName: order.customerFirstName,
      customerLastName: order.customerLastName,
      customerEmail: order.customerEmail,
      customerPhoneNumber: order.customerPhoneNumber,
      itemCount: order.itemCount,
      shippingAddress: order.shippingAddress,
      billingAddress: order.billingAddress,
      paymentInformation: order.paymentInformation,
      lineItems: order.lineItems?.map(item => ({
        itemNumber: item.itemNumber,
        quantity: item.quantity,
        status: item.status,
        priority: item.priority,
      })),
      orderStatusUrl: `${accessEnv('BASE_URL')}/order-status?shopifyOrderNumber=${order.shopifyOrderNumber}&customerEmail=${encodeURIComponent(order.customerEmail)}`,
    };

    return transformedOrder;
  }

  async getRejectedImages(
    orderNumber: string,
    email: string,
    itemNumber: string,
  ) {
    // Validate order and customer
    const order = await DBHelper.findOne(this.orderRepository, {
      where: {
        shopifyOrderNumber: orderNumber,
        customerEmail: email,
      },
      relations: ['lineItems'],
    });
    if (!order) {
      throw new HttpException(
        {
          statusCode: HttpStatus.NOT_FOUND,
          message: 'Order not found',
          error: 'Not Found',
        },
        HttpStatus.NOT_FOUND,
      );
    }
    const lineItem = order.lineItems.find(
      item => item.itemNumber === itemNumber,
    );
    if (!lineItem) {
      throw new HttpException(
        {
          statusCode: HttpStatus.NOT_FOUND,
          message: 'Line item not found in order',
          error: 'Not Found',
        },
        HttpStatus.NOT_FOUND,
      );
    }
    const rejectedImages = await this.attachmentRepository.find({
      where: { lineItem: { id: lineItem.id }, status: 'New Image Requested' },
      order: { createdAt: 'DESC' },
    });
    if (!rejectedImages.length) {
      throw new HttpException(
        {
          statusCode: HttpStatus.NOT_FOUND,
          message: 'No rejected images found for this item',
          error: 'Not Found',
        },
        HttpStatus.NOT_FOUND,
      );
    }
    return {
      orderNumber: orderNumber,
      itemNumber: itemNumber,
      images: rejectedImages.map(img => ({
        id: img.id,
        filename: img.filename,
        url: img.url,
        status: img.status,
        uploadedAt: img.createdAt,
      })),
    };
  }

  async uploadImagesToReplaceRejected(
    orderNumber: string,
    customerEmail: string,
    uploads: Array<{ rejectedAttachmentId: string; imageUrl: string }>,
  ) {
    // First validate the order and customer email
    const order = await DBHelper.findOne(this.orderRepository, {
      where: {
        shopifyOrderNumber: orderNumber,
        customerEmail: customerEmail,
      },
      relations: ['lineItems'],
    });

    if (!order) {
      throw new HttpException(
        {
          statusCode: HttpStatus.NOT_FOUND,
          message: 'Order not found',
          error: 'Not Found',
        },
        HttpStatus.NOT_FOUND,
      );
    }

    const uploadResults: Array<{
      rejectedAttachmentId: string;
      newAttachmentId: string;
      imageUrl: string;
    }> = [];

    // Process each upload
    for (const upload of uploads) {
      const { rejectedAttachmentId, imageUrl } = upload;

      // Find the rejected attachment
      const rejectedAttachment = await DBHelper.findOne(this.attachmentRepository, {
        where: { id: rejectedAttachmentId },
        relations: ['lineItem'],
      });

      if (!rejectedAttachment) {
        throw new HttpException(
          {
            statusCode: HttpStatus.NOT_FOUND,
            message: `Rejected attachment with ID ${rejectedAttachmentId} not found`,
            error: 'Not Found',
          },
          HttpStatus.NOT_FOUND,
        );
      }

      // Verify the attachment belongs to this order
      const lineItem = order.lineItems.find(
        item => item.id === rejectedAttachment.lineItem.id,
      );

      if (!lineItem) {
        throw new HttpException(
          {
            statusCode: HttpStatus.FORBIDDEN,
            message: 'Attachment does not belong to this order',
            error: 'Forbidden',
          },
          HttpStatus.FORBIDDEN,
        );
      }

      // Verify the attachment has "New Image Requested" status
      if (rejectedAttachment.status !== 'New Image Requested') {
        throw new HttpException(
          {
            statusCode: HttpStatus.BAD_REQUEST,
            message: `Attachment ${rejectedAttachmentId} is not in "New Image Requested" status`,
            error: 'Bad Request',
          },
          HttpStatus.BAD_REQUEST,
        );
      }

      const newAttachment = this.attachmentRepository.create({
        filename: `new_image_${Date.now()}.jpg`,
        url: imageUrl,
        mimetype: 'image/jpeg',
        lineItem: rejectedAttachment.lineItem,
      });

      const savedAttachment =
        await this.attachmentRepository.save(newAttachment);

      // Update the rejected attachment status to indicate it has been replaced
      rejectedAttachment.status = 'replaced';
      await this.attachmentRepository.save(rejectedAttachment);

      uploadResults.push({
        rejectedAttachmentId: rejectedAttachmentId,
        newAttachmentId: savedAttachment.id,
        imageUrl: savedAttachment.url,
      });
    }

    return {
      message: 'Images uploaded successfully',
      uploads: uploadResults,
    };
  }
}
