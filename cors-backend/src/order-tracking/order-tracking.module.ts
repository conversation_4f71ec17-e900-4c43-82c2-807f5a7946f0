import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OrderTrackingController } from './order-tracking.controller';
import { OrderTrackingService } from './order-tracking.service';
import { Order } from '../orders/entities/order.entity';
import { LineItem } from '../orders/entities/line-item.entity';
import { ProductSku } from '../product-sku/entities/product-sku.entity';
import { Attachment } from '../attachments/entities/attachment.entity';
import { EmailUtil } from '../utils/email.util';

@Module({
  imports: [
    TypeOrmModule.forFeature([Order, LineItem, ProductSku, Attachment]),
  ],
  controllers: [OrderTrackingController],
  providers: [OrderTrackingService, EmailUtil],
  exports: [OrderTrackingService],
})
export class OrderTrackingModule {} 