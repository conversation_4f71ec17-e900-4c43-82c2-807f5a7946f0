import {
  Controller,
  Post,
  Get,
  Body,
  Query,
  UsePipes,
  ValidationPipe,
  HttpException,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiQuery,
} from '@nestjs/swagger';
import { OrderTrackingService } from './order-tracking.service';
import { ShopifyTokenGuard } from '../auth/guards/shopify-token.guard';
import { OrderTrackingRequestDto } from './dto/order-tracking-request.dto';
import { UploadImageRequestDto } from './dto/upload-image-request.dto';

@UseGuards(ShopifyTokenGuard)
@ApiTags('Order Tracking')
@Controller('order-tracking')
@UsePipes(
  new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidNonWhitelisted: true,
  }),
)
export class OrderTrackingController {
  constructor(private readonly orderTrackingService: OrderTrackingService) {}

  @Post()
  @ApiOperation({
    summary: 'Get order details by order number and customer email',
    description:
      'API to retrieve order details using the shopify order number and customer email for validation. Requires fixed customer access token.',
  })
  @ApiBody({
    type: OrderTrackingRequestDto,
    description: 'Order tracking request with order number and email',
    examples: {
      example1: {
        value: {
          orderNumber: '12345',
          email: '<EMAIL>',
        },
        summary: 'Example order tracking request',
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Order details retrieved successfully',
  })
  async getOrderDetails(@Body() request: OrderTrackingRequestDto) {
    try {
      return await this.orderTrackingService.getOrderDetails(
        request.orderNumber,
        request.email,
      );
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Internal server error',
          error: 'Internal Server Error',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get()
  @ApiOperation({
    summary: 'Get order details by order number and customer email (GET)',
    description:
      'API to retrieve order details using the shopify order number and customer email as query parameters. Requires fixed customer access token.',
  })
  @ApiQuery({
    name: 'orderNumber',
    description: 'The shopify order number (e.g., 12345)',
    example: 'CC-12345',
    required: true,
  })
  @ApiQuery({
    name: 'email',
    description: 'Customer email address for validation',
    example: '<EMAIL>',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Order details retrieved successfully',
  })
  async getOrderDetailsByQuery(
    @Query('orderNumber') orderNumber: string,
    @Query('email') email: string,
  ) {
    try {
      if (!orderNumber || !email) {
        throw new HttpException(
          'Order number and email are required',
          HttpStatus.BAD_REQUEST,
        );
      }
      return await this.orderTrackingService.getOrderDetails(
        orderNumber,
        email,
      );
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Internal server error',
          error: 'Internal Server Error',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('upload-new-images')
  @ApiOperation({
    summary: 'Upload new images to replace rejected images',
    description:
      'Upload multiple new images to replace specific rejected images. Each new image is associated with a rejected attachment ID. All uploads are processed in a single request.',
  })
  @ApiBody({
    description: 'Image URLs and order details',
    type: UploadImageRequestDto,
    examples: {
      example1: {
        value: {
          orderNumber: '12345',
          email: '<EMAIL>',
          uploads: [
            {
              rejectedAttachmentId: 'uuid1',
              imageUrl: 'https://storage.example.com/new_pet_photo_1.jpg',
            },
            {
              rejectedAttachmentId: 'uuid2',
              imageUrl: 'https://storage.example.com/new_pet_photo_2.jpg',
            },
          ],
        },
        summary: 'Example upload request',
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Images uploaded successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request - missing required fields or invalid URLs',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing customer access token',
  })
  @ApiResponse({
    status: 403,
    description: 'Invalid order number or email combination',
  })
  @ApiResponse({
    status: 404,
    description: 'Order or rejected attachment not found',
  })
  async uploadImagesToReplaceRejected(@Body() body: UploadImageRequestDto) {
    try {
      if (!body.uploads || body.uploads.length === 0) {
        throw new HttpException(
          'At least one image upload is required',
          HttpStatus.BAD_REQUEST,
        );
      }

      return await this.orderTrackingService.uploadImagesToReplaceRejected(
        body.orderNumber,
        body.email,
        body.uploads,
      );
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Internal server error',
          error: 'Internal Server Error',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('rejected-image')
  @ApiOperation({
    summary: 'Get rejected image(s) for a line item',
    description:
      'Fetch the most recent rejected image(s) (status: "new image requested") for a specific line item, identified by order number, customer email, and item number.',
  })
  @ApiQuery({
    name: 'orderNumber',
    description: 'Shopify order number',
    example: '12345',
    required: true,
  })
  @ApiQuery({
    name: 'email',
    description: 'Customer email address for validation',
    example: '<EMAIL>',
    required: true,
  })
  @ApiQuery({
    name: 'itemNumber',
    description: 'Line item number',
    example: 'CC-ITEM-001',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Rejected image(s) retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Order, item, or rejected image not found',
  })
  async getRejectedImages(
    @Query('orderNumber') orderNumber: string,
    @Query('email') email: string,
    @Query('itemNumber') itemNumber: string,
  ) {
    return this.orderTrackingService.getRejectedImages(
      orderNumber,
      email,
      itemNumber,
    );
  }
}
