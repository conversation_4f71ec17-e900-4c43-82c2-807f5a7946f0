import { AttachmentStatus } from 'src/orders/enums/attachment-status.enum';

export const ATTACHMENT_STATUS_TRANSITIONS: Partial<
  Record<AttachmentStatus, AttachmentStatus[]>
> = {
  [AttachmentStatus.PENDING]: [
    AttachmentStatus.MANUAL_CROP_NEEDED,
    AttachmentStatus.CROP_APPROVED,
    AttachmentStatus.CUTOUT_PRO_FAILED,
    AttachmentStatus.READY_FOR_REVIEW,
  ],
  [AttachmentStatus.READY_FOR_REVIEW]: [
    AttachmentStatus.CROP_APPROVED,
    AttachmentStatus.CROP_DENIED,
    AttachmentStatus.NEW_IMAGE_REQUESTED,
  ],
  [AttachmentStatus.CROP_DENIED]: [AttachmentStatus.CROP_APPROVED],
};
