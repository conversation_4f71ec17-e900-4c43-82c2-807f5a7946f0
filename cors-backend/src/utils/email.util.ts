import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import { RECIPIENT_EMAILS } from 'src/constants/emails.constant';

@Injectable()
export class EmailUtil {
  private transporter: nodemailer.Transporter;
  private readonly logger = new Logger(EmailUtil.name);

  constructor(private configService: ConfigService) {
    this.transporter = nodemailer.createTransport({
      host: this.configService.get('SMTP_HOST'),
      port: this.configService.get('SMTP_PORT'),
      auth: {
        user: this.configService.get('SMTP_USERNAME'),
        pass: this.configService.get('SMTP_PASSWORD'),
      },
    });
  }

  async sendPasswordResetEmail(
    email: string,
    resetToken: string,
  ): Promise<void> {
    const resetUrl = `${this.configService.get('FRONTEND_URL')}/reset-password?token=${resetToken}`;

    const mailOptions = {
      from: `"Cuddle Clones - CORS" <${this.configService.get('SMTP_FROM_EMAIL')}>`,
      to: email,
      subject: 'Password Reset Request',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">Password Reset Request</h2>
          <p>You have requested to reset your password. Click the button below to reset your password:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" 
               style="background-color: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
              Reset Password
            </a>
          </div>
          <p>If you did not request this password reset, please ignore this email.</p>
          <p>This link will expire in 1 hour.</p>
          <hr style="border: 1px solid #eee; margin: 20px 0;">
          <p style="color: #666; font-size: 12px;">
            If the button above doesn't work, copy and paste this link into your browser:<br>
            ${resetUrl}
          </p>
        </div>
      `,
    };

    try {
      await this.transporter.sendMail(mailOptions);
    } catch (error) {
      console.error('Error sending password reset email:', error);
      throw new Error('Failed to send password reset email');
    }
  }

  async sendQueueCreationEmail(artworkQueueName: string): Promise<void> {
    this.logger.log(
      `Attempting to send email for artwork queue: ${artworkQueueName}`,
    );

    const fromEmail = this.configService.get('SMTP_FROM_EMAIL');
    if (!fromEmail) {
      throw new Error('SMTP_FROM_EMAIL not configured');
    }

    const mailOptions = {
      from: `"Cuddle Clones - CORS" <${fromEmail}>`,
      to: RECIPIENT_EMAILS.ARTWORK_QUEUE,
      subject: 'Artwork Queue Created',
      html: `
        <p>
          Hi Team,
        </p>
        <p>
          The following artwork queue has been created. Kindly add the relevant permissions and ensure they are assigned to the appropriate users.
        </p>
        <p>
          <strong>${artworkQueueName}</strong>
        </p>
        <p>
          Thank You,<br>
          The Cuddle Clones Team
        </p>
      `,
    };

    try {
      this.logger.log('Sending email...');
      const info = await this.transporter.sendMail(mailOptions);
      this.logger.log(`Email sent successfully: ${JSON.stringify(info)}`);
    } catch (error) {
      this.logger.error('Error sending email:', error);
      throw new Error(`Failed to send email: ${error.message}`);
    }
  }

  async sendOrderResyncEmail(
    syncedOrders: number[],
    notSyncedOrders: number[],
    shopName: string,
  ): Promise<void> {
    this.logger.log(`Attempting to send email for shop: ${shopName}`);
    this.logger.log(
      `Synced orders: ${syncedOrders.length}, Not synced orders: ${notSyncedOrders.length}`,
    );

    const fromEmail = this.configService.get('SMTP_FROM_EMAIL');
    if (!fromEmail) {
      throw new Error('SMTP_FROM_EMAIL not configured');
    }

    const mailOptions = {
      from: `"Cuddle Clones - CORS" <${fromEmail}>`,
      to: RECIPIENT_EMAILS.ORDER_RESYNC,
      subject: 'CORS Missing Orders',
      html: `
        <p>
          Hi Customer Care,
        </p>
        ${
          syncedOrders.length > 0
            ? `
          <p>
            The following orders have been resync to OMS from store ${shopName}:
          </p>
          <ul>
            ${syncedOrders.map(order => `<li>${order}</li>`).join('')}
          </ul>
        `
            : ''
        }
        ${
          notSyncedOrders.length > 0
            ? `
          <p>
            The following orders have not been resync to CORS by cron:
          </p>
          <ul>
            ${notSyncedOrders.map(order => `<li>${order}</li>`).join('')}
          </ul>
        `
            : ''
        }
        <p>
          Thank You,<br>
          The Cuddle Clones Team
        </p>
      `,
    };

    try {
      this.logger.log('Sending email...');
      const info = await this.transporter.sendMail(mailOptions);
      this.logger.log(`Email sent successfully: ${JSON.stringify(info)}`);
    } catch (error) {
      this.logger.error('Error sending email:', error);
      throw new Error(`Failed to send email: ${error.message}`);
    }
  }

  async sendImageRequestEmail(
    customerEmail: string,
    customerName: string,
    orderNumber: string,
    lineItems: Array<{
      itemNumber: string;
      quantity: number;
      productName?: string;
    }>,
    uploadUrl?: string,
  ): Promise<void> {
    const frontendUrl =
      this.configService.get('FRONTEND_URL') || 'http://localhost:3000';
    const uploadLink =
      uploadUrl ||
      `${frontendUrl}/order-status?shopifyOrderNumber=${orderNumber}&customerEmail=${encodeURIComponent(customerEmail)}&newImageRequest`;

    const lineItemsHtml = lineItems
      .map(
        item => `
        <tr>
          <td style="padding: 10px; border-bottom: 1px solid #eee;">${item.itemNumber}</td>
          <td style="padding: 10px; border-bottom: 1px solid #eee;">${item.productName || 'Custom Product'}</td>
          <td style="padding: 10px; border-bottom: 1px solid #eee;">${item.quantity}</td>
        </tr>
      `,
      )
      .join('');

    const mailOptions = {
      from: `"Cuddle Clones - CORS" <${this.configService.get('SMTP_FROM_EMAIL')}>`,
      to: customerEmail,
      subject: `Image Request for Order ${orderNumber}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f9f9f9; padding: 20px;">
          <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #333; margin: 0;">🐾 Cuddle Clones</h1>
              <p style="color: #666; margin: 10px 0 0 0;">Image Request Required</p>
            </div>
            
            <h2 style="color: #333; border-bottom: 2px solid #4CAF50; padding-bottom: 10px;">
              Hello ${customerName}!
            </h2>
            
            <p style="color: #555; line-height: 1.6;">
              Thank you for your order! To create your custom Cuddle Clone, we need high-quality photos of your beloved pet.
            </p>
            
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0;">
              <h3 style="color: #333; margin-top: 0;">📋 Order Details</h3>
              <p><strong>Order Number:</strong> ${orderNumber}</p>
              
              <h4 style="color: #555; margin: 20px 0 10px 0;">Items Requiring Images:</h4>
              <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                <thead>
                  <tr style="background-color: #4CAF50; color: white;">
                    <th style="padding: 10px; text-align: left;">Item #</th>
                    <th style="padding: 10px; text-align: left;">Product</th>
                    <th style="padding: 10px; text-align: left;">Quantity</th>
                  </tr>
                </thead>
                <tbody>
                  ${lineItemsHtml}
                </tbody>
              </table>
            </div>
            
            <div style="background-color: #e8f5e8; padding: 20px; border-radius: 6px; margin: 20px 0;">
              <h3 style="color: #2e7d32; margin-top: 0;">📸 Upload Your Images</h3>
              <p style="color: #555; margin-bottom: 20px;">
                Please upload clear, high-quality photos of your pet. For best results:
              </p>
              <ul style="color: #555; line-height: 1.6;">
                <li>Use good lighting (natural light is best)</li>
                <li>Take photos from multiple angles</li>
                <li>Ensure your pet's face is clearly visible</li>
                <li>Upload photos in JPG, PNG, or HEIC format</li>
                <li>Maximum file size: 10MB per image</li>
              </ul>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${uploadLink}" 
                 style="background-color: #4CAF50; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold; font-size: 16px;">
                📤 Upload Images Now
              </a>
            </div>
            
            <div style="background-color: #fff3cd; padding: 15px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #ffc107;">
              <h4 style="color: #856404; margin-top: 0;">⏰ Important</h4>
              <p style="color: #856404; margin: 0; line-height: 1.5;">
                Please upload your images within 7 days to avoid delays in processing your order. 
                If you need assistance, please contact our customer support team.
              </p>
            </div>
            
            <hr style="border: 1px solid #eee; margin: 30px 0;">
            
            <div style="text-align: center; color: #666; font-size: 14px;">
              <p>If the button above doesn't work, copy and paste this link into your browser:</p>
              <p style="word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 4px;">
                ${uploadLink}
              </p>
            </div>
            
            <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
              <p style="color: #666; font-size: 12px;">
                Thank you for choosing Cuddle Clones!<br>
                Questions? Contact <NAME_EMAIL>
              </p>
            </div>
          </div>
        </div>
      `,
    };

    try {
      await this.transporter.sendMail(mailOptions);
      this.logger.log(
        `Image Request Email sent successfully to ${customerEmail} for order ${orderNumber}`,
      );
    } catch (error) {
      console.error('Error sending image request email:', error);
      throw new Error('Failed to send image request email');
    }
  }
}
