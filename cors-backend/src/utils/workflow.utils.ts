import { BadRequestException } from '@nestjs/common';
import { STATUS_TRANSITIONS } from 'src/common/line-item-status.transitions';
import { ATTACHMENT_STATUS_TRANSITIONS } from 'src/common/attachment-status.transitions';
import { User } from 'src/users/entities/user.entity';

export function checkStatusTransition(
  currentStatus: string,
  newStatus: string,
) {
  const allowedStatuses = STATUS_TRANSITIONS[currentStatus];
  if (!allowedStatuses || !allowedStatuses.includes(newStatus)) {
    throw new BadRequestException(
      `Cannot transition from "${currentStatus}" to "${newStatus}"`,
    );
  }
}

export function checkAttachmentStatusTransition(
  currentStatus: string,
  newStatus: string,
) {
  const allowedStatuses = ATTACHMENT_STATUS_TRANSITIONS[currentStatus];
  if (!allowedStatuses || !allowedStatuses.includes(newStatus)) {
    throw new BadRequestException(
      `Cannot transition from "${currentStatus}" to "${newStatus}"`,
    );
  }
}

export function userFullName(user: User) {
  if (!user) return '';
  const firstName = user.firstName || '';
  const lastName = user.lastName || '';
  return `${firstName} ${lastName}`.trim();
}
