import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Queue } from './entities/queue.entity';
import { QueuesService } from './workflow-queues.service';
import { QueuesController } from './workflow-queues.controller';
import { Attachment } from 'src/attachments/entities/attachment.entity';
import { LineItem } from 'src/orders/entities/line-item.entity';
import { User } from 'src/users/entities/user.entity';
import { EmailUtil } from 'src/utils/email.util';

@Module({
  imports: [TypeOrmModule.forFeature([Queue, Attachment, LineItem, User])],
  providers: [QueuesService, EmailUtil],
  controllers: [QueuesController],
  exports: [QueuesService],
})
export class QueuesModule {}
