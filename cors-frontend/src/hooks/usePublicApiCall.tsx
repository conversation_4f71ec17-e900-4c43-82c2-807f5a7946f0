'use client';
import { useState, useCallback, useEffect } from 'react';
import { publicApiCall } from '@/actions/public-order-status';
import { toast } from 'react-toastify';

type ApiMethod = 'get' | 'post' | 'put' | 'delete' | 'patch';

interface QueryParams {
  [key: string]: string | number | boolean;
}

interface UrlParams {
  [key: string]: string | number;
}

interface ApiOptions {
  queryParams?: QueryParams;
  urlParams?: UrlParams;
  body?: any;
}

export function usePublicApiCall<T = any>(
  url: string,
  method: ApiMethod = 'get',
  tryOnRender: boolean = false,
  options?: ApiOptions,
) {
  const [data, setData] = useState<T>();
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState<any>(null);

  const buildUrl = useCallback(
    (baseUrl: string, urlParams?: UrlParams, queryParams?: QueryParams) => {
      let finalUrl = baseUrl;

      // Replace URL parameters
      if (urlParams) {
        Object.entries(urlParams).forEach(([key, value]) => {
          finalUrl = finalUrl.replace(`:${key}`, String(value));
        });
      }

      // Add query parameters
      if (queryParams) {
        const queryString = Object.entries(queryParams)
          .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`)
          .join('&');

        if (queryString) {
          finalUrl += (finalUrl.includes('?') ? '&' : '?') + queryString;
        }
      }

      return finalUrl;
    },
    [],
  );

  const fetchData = useCallback(
    async (overrideOptions?: ApiOptions) => {
      setIsLoading(true);
      setError(null);
      try {
        const finalUrl = buildUrl(
          url,
          overrideOptions?.urlParams || options?.urlParams,
          overrideOptions?.queryParams || options?.queryParams,
        );

        const response = await publicApiCall<T>(
          method,
          finalUrl,
          overrideOptions?.body || options?.body,
          // Query params are handled in URL building for public API
        );

        setData(response);
        setIsSuccess(true);
        return response;
      } catch (err) {
        setError(err);
        // Don't show toast here as public API errors are already handled in publicApiCall
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [url, method, buildUrl, options],
  );

  useEffect(() => {
    if (tryOnRender) {
      fetchData();
    }
  }, [tryOnRender, fetchData]);

  return { data, isLoading, isSuccess, error, makeRequest: fetchData };
}

export default usePublicApiCall;
