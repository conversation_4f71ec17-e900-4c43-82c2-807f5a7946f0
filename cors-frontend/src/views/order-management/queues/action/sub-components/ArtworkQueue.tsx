'use client';
import Button from '@/components/Button';
import { Box, Chip, Dialog, DialogContent, DialogTitle, Divider, Typography } from '@mui/material';
import Grid from '@mui/material/Grid2';
import SingleImageViewCard from '@/components/card-statistics/SingleImageViewCard';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import ImageUploadField from '@/@core/components/mui/ImageUpload';
import { useEffect, useState } from 'react';
import ProductAttributesViewer from '../../components/ProductAttributesViewer';
import { SingleQueueItem } from '@/types/queues.types';
import { useRouter } from 'next/navigation';
import LoadingView from '@/components/LoadingView';
import { toast } from 'react-toastify';
import useApiCall from '@/hooks/useApiCall';
import AddArtworkRequest from './AddArtworkRequest';
import { handleDownload } from '@/services/queues.services';
type FormType = {
  image: string[];
};
const ArtworkQueue = ({
  queueItem,
  queueId,
  actionType,
}: {
  queueItem: SingleQueueItem | undefined;
  queueId: string;
  actionType: string;
}) => {
  const router = useRouter();
  const [showAddRequest, setShowAddRequest] = useState(false);
  const [upcomingQueue, setUpcomingQueue] = useState<SingleQueueItem | undefined>(undefined);
  const { isLoading: loading, makeRequest: requestUpdateQueueItem } = useApiCall<SingleQueueItem>(
    `/workflow-queues/upload-template-placement-file`,
    'post',
    false,
  );

  useEffect(() => {
    if (upcomingQueue) {
      if (upcomingQueue?.lineItems == null) {
        router.push(`/ordermanagement/queues/?tab=${queueId}`);
      }
    }
  }, [upcomingQueue, router]);

  const {
    handleSubmit,
    control,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<FormType>({
    resolver: yupResolver(
      yup.object().shape({
        image: yup
          .array()
          .of(yup.string().required())
          .min(1, 'At least one art file is required')
          .max(1, 'Only one art file is allowed')
          .required('Art file is required'),
      }),
    ),
    defaultValues: {
      image: [],
    },
  });

  const onSubmit = async (data: FormType) => {
    const response = await requestUpdateQueueItem({
      body: {
        lineItemId: queueItem?.lineItems?.id,
        templatePlacementFileUrl: data?.image[0],
        queueId: queueId,
      },
    });
    if (response) {
      setUpcomingQueue(response);
      toast.success('Queue item updated successfully');
    }
  };

  if (loading) return <LoadingView />;

  if (upcomingQueue?.lineItems) {
    return <ArtworkQueue queueItem={upcomingQueue} queueId={queueId} actionType={actionType} />;
  }

  return (
    <>
      <ProductAttributesViewer actionType={actionType} queueItem={queueItem} />
      {/* Images Section */}
      <Grid container spacing={4} sx={{ mb: 4 }}>
        {queueItem?.lineItems?.attachments?.map((attachment, index) => (
          <Grid
            key={index}
            size={{
              xs: 12,
              md:
                queueItem?.lineItems?.attachments && queueItem?.lineItems?.attachments?.length >= 3
                  ? 4
                  : 6,
            }}
          >
            <SingleImageViewCard
              imageUrl={attachment.attachment_url}
              title={`Pet ${index + 1}`}
              downloadUrl={attachment.attachment_url}
              imageName={attachment?.attachment_url?.split('/').pop() || ''}
            />
          </Grid>
        ))}
      </Grid>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Controller
          name={`image`}
          control={control}
          rules={{ required: 'Image is required' }}
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <ImageUploadField
              key="image-upload-field"
              control={control}
              errors={errors}
              setValue={setValue}
              name={`image`}
              minImages={1}
              maxImages={1}
              maxFileSizeMB={5}
              formValue={value}
              errorField={error?.message}
              title="Upload Art File"
              buttonText="Upload"
            />
          )}
        />

        {queueItem?.lineItems?.rejected_attachments &&
          queueItem?.lineItems?.rejected_attachments?.length > 0 && (
            <Box sx={{ mt: 4, maxWidth: '50%' }}>
              <Typography variant="h6">Revision Completed Art Files</Typography>
              {/* <Divider sx={{ my: 2 }} /> */}
              {queueItem?.lineItems?.rejected_attachments?.map((attachment, index) => (
                <Box
                  key={index}
                  sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    gap: 2,
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'row',
                      gap: 2,
                      alignItems: 'center',
                      my: 1,
                    }}
                  >
                    <Typography variant="body1">
                      {attachment.created_at
                        ? new Date(attachment.created_at)
                            .toLocaleString('en-US', {
                              month: 'short',
                              day: 'numeric',
                              year: 'numeric',
                            })
                            .toUpperCase()
                        : '-'}
                    </Typography>
                    <Divider orientation="vertical" variant="middle" flexItem />

                    <Typography variant="body1">
                      {attachment.created_at
                        ? new Date(attachment.created_at).toLocaleTimeString()
                        : '-'}
                    </Typography>
                    <Divider orientation="vertical" variant="middle" flexItem />

                    <Typography variant="body1">{queueItem?.lineItems?.assigned_to}</Typography>
                  </Box>
                  <Chip
                    label={attachment?.completed_art_file_url?.split('/').pop() || ''}
                    onClick={() =>
                      handleDownload(
                        attachment?.completed_art_file_url || '',
                        `${attachment?.completed_art_file_url?.split('/').pop()}.png`,
                      )
                    }
                    size="small"
                    variant="outlined"
                    style={{ marginRight: 4, marginTop: 4 }}
                  />
                </Box>
              ))}
            </Box>
          )}
        {/* Actions Section */}
        <Box
          sx={{
            display: 'flex',
            gap: 2,
            justifyContent: 'space-between',
            mt: 8,
            alignItems: 'center',
          }}
        >
          <Box>
            <Button
              variant="outlined"
              fullWidth
              size="small"
              title="Add Request"
              type="button"
              sx={{ width: '200px' }}
              onClick={() => setShowAddRequest(!showAddRequest)}
            />
          </Box>
          <Box sx={{ display: 'flex', gap: 2, height: '100%' }}>
            <Button
              variant="outlined"
              fullWidth
              size="small"
              color="warning"
              title="Customer Contact Needed"
              type="button"
              disabled={isSubmitting}
              sx={{ width: '250px' }}
            />
            <Button
              variant="outlined"
              fullWidth
              size="small"
              title="Submit"
              type="submit"
              disabled={isSubmitting}
              sx={{ width: '250px' }}
            />
          </Box>
        </Box>
      </form>
      <Dialog
        open={showAddRequest}
        onClose={() => setShowAddRequest(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Add Artwork Request</DialogTitle>
        <DialogContent>
          <AddArtworkRequest />
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ArtworkQueue;
