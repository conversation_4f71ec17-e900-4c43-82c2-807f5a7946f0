'use client';
import React, { useState, useEffect } from 'react';
import { Container, Typography, Box, Alert, CircularProgress, Paper } from '@mui/material';
import { PublicOrderStatus } from '@/types/public-order-status.types';
import OrderDetailsCard from '@/components/PublicOrderStatus/OrderDetailsCard';
import LineItemsTable from '@/components/PublicOrderStatus/LineItemsTable';
import { fetchPublicOrderStatus } from '@/actions/public-order-status';

interface PublicOrderStatusPageProps {
  initialOrderData?: PublicOrderStatus | null;
  initialError?: string | null;
  shopifyOrderNumber?: string;
  customerEmail?: string;
  lineItemId?: string;
}

const PublicOrderStatusPage: React.FC<PublicOrderStatusPageProps> = ({
  initialOrderData,
  initialError,
  shopifyOrderNumber,
  customerEmail,
}) => {
  const [orderData, setOrderData] = useState<PublicOrderStatus | null>(initialOrderData || null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(initialError || null);

  useEffect(() => {
    if (initialOrderData) {
      setOrderData(initialOrderData);
    }
    if (initialError) {
      setError(initialError);
    }
  }, [initialOrderData, initialError]);

  const refreshOrderData = async () => {
    if (!shopifyOrderNumber || !customerEmail) return;

    setLoading(true);
    setError(null);

    try {
      const refreshedData = await fetchPublicOrderStatus(shopifyOrderNumber, customerEmail);
      setOrderData(refreshedData);
    } catch (err) {
      setError('Failed to refresh order data. Please try again.');
      console.error('Error refreshing order data:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box textAlign="center" mb={4}>
        <Typography variant="h3" component="h1" gutterBottom color="primary">
          Order Status
        </Typography>
        <Typography variant="h6" color="text.secondary">
          Track your order progress and request updates
        </Typography>
      </Box>

      {loading && (
        <Box
          display="flex"
          flexDirection="column"
          justifyContent="center"
          alignItems="center"
          py={6}
          gap={2}
        >
          <CircularProgress size={60} thickness={4} />
          <Typography variant="h6" color="text.secondary">
            Loading order details...
          </Typography>
        </Box>
      )}

      {/* Error State */}
      {error && !loading && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Order Data */}
      {orderData && !loading && 'lineItems' in orderData && (
        <Box>
          <OrderDetailsCard orderData={orderData as PublicOrderStatus} />
          <LineItemsTable
            lineItems={(orderData as PublicOrderStatus).lineItems}
            orderData={orderData as PublicOrderStatus}
            onOrderUpdate={refreshOrderData}
          />
        </Box>
      )}

      {/* Help Section */}
      <Paper elevation={1} sx={{ p: 3, mt: 4 }}>
        <Typography variant="h6" gutterBottom>
          Need Help?
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          If you're having trouble with your order or need assistance, please contact our customer
          support team.
        </Typography>
        <Typography variant="body2" color="text.secondary">
          • Make sure you're providing the correct order information
        </Typography>
      </Paper>
    </Container>
  );
};

export default PublicOrderStatusPage;
