'use server';

import apiClient from '@/utils/axios';

export async function UploadImage(formData: FormData): Promise<string> {
  try {
    const response = await apiClient.post('/attachments/upload-image', formData);
    if (!response.data.url) {
      throw new Error('Failed to upload image');
    }
    return response?.data?.url;
  } catch (error) {
    console.error('Error uploading image:', error);
    throw new Error('Failed to upload image');
  }
}



// / export const uploadImage = async (file: File): Promise<string> => {
//   try {
//     const formData = new FormData();
//     formData.append('file', file);

//     const response = await apiClient.post('/attachments/upload-image', formData, {
//       headers: {
//         'Content-Type': 'multipart/form-data',
//       },
//     });

//     if (response.data && response.data.url) {
//       return response.data.url;
//     } else {
//       throw new Error('Invalid response from server');
//     }
//   } catch (error) {
//     throw error;
//   }
// };
