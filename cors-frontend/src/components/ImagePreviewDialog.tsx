import React, { useRef, useState, useCallback, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Typography,
  Box,
  Fade,
  Tooltip,
} from '@mui/material';
import {
  Close as CloseIcon,
  ZoomIn,
  ZoomOut,
  CenterFocusStrong,
  Fullscreen,
  FullscreenExit,
} from '@mui/icons-material';
import { TransitionProps } from '@mui/material/transitions';

// Smooth fade transition for the image preview dialog
const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement;
  },
  ref: React.Ref<unknown>,
) {
  return <Fade ref={ref} {...props} timeout={{ enter: 400, exit: 300 }} />;
});

interface ImagePreviewDialogProps {
  open: boolean;
  onClose: () => void;
  imageUrl: string | null;
  title?: string;
}

const ImagePreviewDialog = ({
  open,
  onClose,
  imageUrl,
  title = 'Image Preview',
}: ImagePreviewDialogProps) => {
  const [zoomLevel, setZoomLevel] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [isFullscreen, setIsFullscreen] = useState(false);
  const imageRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Reset state when dialog opens/closes
  useEffect(() => {
    if (open) {
      setZoomLevel(1);
      setPosition({ x: 0, y: 0 });
      setIsDragging(false);
      setIsFullscreen(false);
    }
  }, [open]);

  // Handle wheel zoom
  const handleWheel = useCallback(
    (e: React.WheelEvent) => {
      e.preventDefault();
      const delta = e.deltaY > 0 ? -0.2 : 0.2;
      const newZoom = Math.max(0.5, Math.min(5, zoomLevel + delta));
      setZoomLevel(newZoom);

      // Reset position when zooming out to 1x
      if (newZoom <= 1) {
        setPosition({ x: 0, y: 0 });
      }
    },
    [zoomLevel],
  );

  // Handle mouse down for dragging
  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      if (zoomLevel > 1) {
        setIsDragging(true);
        setDragStart({
          x: e.clientX - position.x,
          y: e.clientY - position.y,
        });
      }
    },
    [zoomLevel, position],
  );

  // Handle mouse move for dragging
  const handleMouseMove = useCallback(
    (e: React.MouseEvent) => {
      if (isDragging && zoomLevel > 1) {
        const newX = e.clientX - dragStart.x;
        const newY = e.clientY - dragStart.y;

        // Limit dragging to prevent image from going too far
        const maxOffset = 200;
        setPosition({
          x: Math.max(-maxOffset, Math.min(maxOffset, newX)),
          y: Math.max(-maxOffset, Math.min(maxOffset, newY)),
        });
      }
    },
    [isDragging, dragStart, zoomLevel],
  );

  // Zoom controls
  const handleZoomIn = () => {
    const newZoom = Math.min(5, zoomLevel + 0.5);
    setZoomLevel(newZoom);
  };

  const handleZoomOut = () => {
    const newZoom = Math.max(0.5, zoomLevel - 0.5);
    setZoomLevel(newZoom);
    if (newZoom <= 1) {
      setPosition({ x: 0, y: 0 });
    }
  };

  const handleResetZoom = () => {
    setZoomLevel(1);
    setPosition({ x: 0, y: 0 });
  };

  const handleToggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  return (
    <Dialog
      open={open}
      TransitionComponent={Transition}
      keepMounted
      onClose={onClose}
      maxWidth={isFullscreen ? false : 'lg'}
      fullWidth={!isFullscreen}
      fullScreen={isFullscreen}
      sx={{
        '& .MuiDialog-paper': {
          zIndex: theme => theme.zIndex.modal + 1,
          bgcolor: 'rgba(0, 0, 0, 0.95)',
          ...(isFullscreen && {
            margin: 0,
            maxHeight: '100vh',
            maxWidth: '100vw',
          }),
        },
        zIndex: theme => theme.zIndex.modal + 1,
      }}
    >
      {/* Header with controls */}
      <DialogTitle
        sx={{
          m: 0,
          p: 2,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          bgcolor: 'rgba(0, 0, 0, 0.8)',
          color: 'white',
          backdropFilter: 'blur(10px)',
        }}
      >
        <Typography variant="h6" component="div" sx={{ color: 'white' }}>
          {title}
        </Typography>

        {/* Zoom Controls */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Tooltip title="Zoom Out" arrow>
            <IconButton
              onClick={handleZoomOut}
              disabled={zoomLevel <= 0.5}
              sx={{ color: 'white', '&:disabled': { color: 'grey.600' } }}
            >
              <ZoomOut />
            </IconButton>
          </Tooltip>

          <Typography
            variant="body2"
            sx={{ color: 'white', minWidth: '60px', textAlign: 'center' }}
          >
            {Math.round(zoomLevel * 100)}%
          </Typography>

          <Tooltip title="Zoom In" arrow>
            <IconButton
              onClick={handleZoomIn}
              disabled={zoomLevel >= 5}
              sx={{ color: 'white', '&:disabled': { color: 'grey.600' } }}
            >
              <ZoomIn />
            </IconButton>
          </Tooltip>

          <Tooltip title="Reset Zoom" arrow>
            <IconButton onClick={handleResetZoom} sx={{ color: 'white' }}>
              <CenterFocusStrong />
            </IconButton>
          </Tooltip>

          <Tooltip title={isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'} arrow>
            <IconButton onClick={handleToggleFullscreen} sx={{ color: 'white' }}>
              {isFullscreen ? <FullscreenExit /> : <Fullscreen />}
            </IconButton>
          </Tooltip>

          <Tooltip title="Close" arrow>
            <IconButton onClick={onClose} sx={{ color: 'white' }}>
              <CloseIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </DialogTitle>

      {/* Image Container */}
      <DialogContent
        ref={containerRef}
        sx={{
          p: 0,
          overflow: 'hidden',
          height: isFullscreen ? 'calc(100vh - 64px)' : '80vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: 'rgba(0, 0, 0, 0.95)',
          cursor: zoomLevel > 1 ? (isDragging ? 'grabbing' : 'grab') : 'default',
          userSelect: 'none',
        }}
        onWheel={handleWheel}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={() => setIsDragging(false)}
        onMouseLeave={() => setIsDragging(false)}
      >
        {imageUrl && (
          <Box
            sx={{
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              overflow: 'hidden',
            }}
          >
            <img
              ref={imageRef}
              src={imageUrl}
              alt="Preview"
              draggable={false}
              style={{
                maxWidth: zoomLevel <= 1 ? '95%' : 'none',
                maxHeight: zoomLevel <= 1 ? '95%' : 'none',
                width: zoomLevel > 1 ? 'auto' : undefined,
                height: zoomLevel > 1 ? 'auto' : undefined,
                transform: `scale(${zoomLevel}) translate(${position.x}px, ${position.y}px)`,
                transformOrigin: 'center center',
                transition: isDragging ? 'none' : 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                borderRadius: '8px',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
              }}
            />
          </Box>
        )}

        {/* Zoom Instructions */}
        {zoomLevel <= 1 && (
          <Box
            sx={{
              position: 'absolute',
              bottom: 20,
              left: '50%',
              transform: 'translateX(-50%)',
              bgcolor: 'rgba(0, 0, 0, 0.7)',
              color: 'white',
              px: 2,
              py: 1,
              borderRadius: 2,
              backdropFilter: 'blur(10px)',
              fontSize: '0.875rem',
              opacity: 0.8,
            }}
          >
            Scroll to zoom • Drag to pan when zoomed
          </Box>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default ImagePreviewDialog;
