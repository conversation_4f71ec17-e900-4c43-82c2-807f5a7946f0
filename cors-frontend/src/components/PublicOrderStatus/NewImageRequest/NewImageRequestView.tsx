'use client';

import React, { useState, useEffect } from 'react';
import { Box, Button, Alert, Fade, Zoom } from '@mui/material';
import { CheckCircle, Send } from '@mui/icons-material';
import { toast } from 'react-toastify';
import { PublicOrderStatus } from '@/types/public-order-status.types';
import { UploadImage } from '@/actions/image-upload';
import { fetchRejectedImage, uploadNewImages } from '@/actions/public-order-status';
import { PublicLineItem } from '@/types/public-order-status.types';
import ImagePreviewDialog from '@/components/ImagePreviewDialog';
import RequestHeader from './RequestHeader';
import RejectedImageSection from './RejectedImageSection';

interface NewImageRequestViewProps {
  orderData: PublicOrderStatus;
  formType?: string;
  lineItem?: PublicLineItem;
}

const NewImageRequestView: React.FC<NewImageRequestViewProps> = ({ orderData, lineItem }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [rejectedImageData, setRejectedImageData] = useState<any>(null);
  const [uploadedImages, setUploadedImages] = useState<{
    [rejectedId: string]: { file: File | null; url: string | null; uploading: boolean };
  }>({});
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const effectiveLineItem = lineItem || (orderData.lineItems && orderData.lineItems[0]);

  useEffect(() => {
    async function fetchRejected() {
      if (
        orderData?.shopifyOrderNumber &&
        orderData?.customerEmail &&
        effectiveLineItem?.itemNumber
      ) {
        const data = await fetchRejectedImage(
          orderData.shopifyOrderNumber,
          orderData.customerEmail,
          effectiveLineItem.itemNumber,
        );
        setRejectedImageData(data);
        // Initialize uploadedImages state for each rejected image
        if (data?.images && data.images.length > 0) {
          const initialUploads: {
            [rejectedId: string]: { file: File | null; url: string | null; uploading: boolean };
          } = {};
          data.images.forEach((img: any) => {
            initialUploads[img.id] = { file: null, url: null, uploading: false };
          });
          setUploadedImages(initialUploads);
        }
      }
    }
    fetchRejected();
  }, [orderData?.shopifyOrderNumber, orderData?.customerEmail, effectiveLineItem?.itemNumber]);
  console.log('herere----', rejectedImageData);

  const handleFileChange = async (
    rejectedId: string,
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast.error('Please select an image file');
        return;
      }
      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        toast.error('File size must be less than 10MB');
        return;
      }
      setUploadedImages(prev => ({
        ...prev,
        [rejectedId]: { ...prev[rejectedId], uploading: true, file, url: null },
      }));
      // Force a render before starting upload
      await new Promise(resolve => setTimeout(resolve, 0));
      try {
        const formData = new FormData();
        formData.append('file', file);
        const url = await UploadImage(formData);
        setUploadedImages(prev => ({
          ...prev,
          [rejectedId]: { ...prev[rejectedId], url, uploading: false },
        }));
      } catch (error) {
        toast.error('Failed to upload image');
        setUploadedImages(prev => ({
          ...prev,
          [rejectedId]: { ...prev[rejectedId], uploading: false, file: null, url: null },
        }));
      }
    }
  };

  const handleDeleteImage = (rejectedId: string) => {
    setUploadedImages(prev => ({
      ...prev,
      [rejectedId]: { ...prev[rejectedId], file: null, url: null },
    }));
  };

  const allUploaded =
    rejectedImageData &&
    rejectedImageData.images &&
    rejectedImageData.images.length > 0 &&
    rejectedImageData.images.every((img: any) => uploadedImages[img.id]?.url);

  const rejectedCount = rejectedImageData?.images?.length || 0;
  const uploadedCount = Object.values(uploadedImages).filter(img => img.url).length;
  const uploading = Object.values(uploadedImages).some(img => img.uploading);

  const handleSubmit = async () => {
    if (!allUploaded) {
      toast.error('Please upload a new image for each rejected image');
      return;
    }
    setIsSubmitting(true);
    try {
      const uploads = rejectedImageData.images.map((img: any) => ({
        rejectedAttachmentId: img.id,
        imageUrl: uploadedImages[img.id].url,
      }));
      await uploadNewImages({
        orderNumber: orderData.shopifyOrderNumber,
        email: orderData.customerEmail,
        uploads,
      });
      toast.success('All images submitted successfully!');
      // Optionally reset state or close modal here
    } catch (error) {
      toast.error('Failed to submit images.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box sx={{ maxWidth: 1000, mx: 'auto', p: { xs: 2, md: 3 } }}>
      {/* Header */}
      <RequestHeader
        orderData={orderData}
        lineItem={effectiveLineItem}
        isSubmitting={isSubmitting}
        uploading={uploading}
        rejectedCount={rejectedCount}
        uploadedCount={uploadedCount}
      />

      {/* Main Content */}

      {rejectedImageData && rejectedImageData.images && rejectedImageData.images.length > 0 ? (
        <>
          {/* Rejected Images Section */}
          <RejectedImageSection
            rejectedImages={rejectedImageData.images}
            uploadedImages={uploadedImages}
            onFileChange={handleFileChange}
            onDeleteImage={handleDeleteImage}
            onViewImage={setPreviewImage}
          />

          {/* Submit Section */}
          <Fade in={allUploaded}>
            <Box
              sx={{
                textAlign: 'center',
                p: 4,
                bgcolor: 'success.50',
                borderRadius: 3,
                border: '2px solid',
                borderColor: 'success.200',
                mb: 2,
              }}
            >
              <Zoom in={allUploaded} timeout={500}>
                <CheckCircle
                  sx={{
                    fontSize: 48,
                    color: 'success.main',
                    mb: 2,
                  }}
                />
              </Zoom>

              <Button
                variant="contained"
                size="large"
                startIcon={<Send />}
                onClick={handleSubmit}
                disabled={
                  isSubmitting ||
                  !allUploaded ||
                  Object.values(uploadedImages).some(u => u.uploading)
                }
                sx={{
                  minWidth: 250,
                  py: 2,
                  px: 4,
                  borderRadius: 3,
                  fontSize: '1.2rem',
                  fontWeight: 600,
                  boxShadow: 4,
                  '&:hover': {
                    boxShadow: 8,
                    transform: 'translateY(-2px)',
                  },
                  transition: 'all 0.3s ease',
                }}
              >
                {isSubmitting ? 'Submitting Images...' : 'Submit All Images'}
              </Button>
            </Box>
          </Fade>
        </>
      ) : (
        <Alert
          severity="info"
          sx={{
            borderRadius: 3,
            fontSize: '1.1rem',
            p: 3,
          }}
        >
          No rejected images found for this order. If you believe this is an error, please contact
          support.
        </Alert>
      )}

      {/* Image Preview Dialog */}
      <ImagePreviewDialog
        open={!!previewImage}
        onClose={() => setPreviewImage(null)}
        imageUrl={previewImage}
        title="Image Preview"
      />
    </Box>
  );
};

export default NewImageRequestView;
