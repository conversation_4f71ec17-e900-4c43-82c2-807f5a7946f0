'use client';

import React from 'react';
import { Card, CardContent, Typography, Box, Avatar, Chip, LinearProgress } from '@mui/material';
import { PhotoCamera, Info, Schedule } from '@mui/icons-material';
import { PublicOrderStatus, PublicLineItem } from '@/types/public-order-status.types';

interface RequestHeaderProps {
  orderData: PublicOrderStatus;
  lineItem?: PublicLineItem;
  isSubmitting?: boolean;
  uploading?: boolean;
  rejectedCount?: number;
  uploadedCount?: number;
}

const RequestHeader: React.FC<RequestHeaderProps> = ({
  orderData,
  lineItem,
  isSubmitting = false,
  uploading = false,
  rejectedCount = 0,
  uploadedCount = 0,
}) => {
  const effectiveLineItem = lineItem || (orderData.lineItems && orderData.lineItems[0]);

  return (
    <>
      {/* Main Header Card */}
      <Card
        elevation={6}
        sx={{
          mb: 4,
          borderRadius: 4,
          overflow: 'hidden',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          position: 'relative',
        }}
      >
        {/* Background Pattern */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            right: 0,
            width: '40%',
            height: '100%',
            opacity: 0.1,
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}
        />

        <CardContent sx={{ py: 4, px: 4, position: 'relative', zIndex: 1 }}>
          <Box display="flex" alignItems="center" gap={3} mb={3}>
            <Avatar
              sx={{
                bgcolor: 'rgba(255,255,255,0.2)',
                width: 72,
                height: 72,
                backdropFilter: 'blur(10px)',
              }}
            >
              <PhotoCamera sx={{ fontSize: 36 }} />
            </Avatar>
            <Box flex={1}>
              <Typography
                variant="h3"
                component="h1"
                fontWeight="bold"
                sx={{ mb: 1, fontSize: { xs: '2rem', md: '3rem' } }}
              >
                New Image Request
              </Typography>
              <Typography
                variant="h6"
                sx={{
                  opacity: 0.9,
                  fontWeight: 400,
                  fontSize: { xs: '1rem', md: '1.25rem' },
                }}
              >
                Upload replacement images for your order
              </Typography>
            </Box>
          </Box>

          {/* Order Information */}
          <Box
            sx={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: 4,
              p: 3,
              bgcolor: 'rgba(255, 255, 255, 0.1)',
              borderRadius: 3,
              backdropFilter: 'blur(10px)',
            }}
          >
            <Box>
              <Typography variant="body1" sx={{ opacity: 0.8, mb: 0.5 }}>
                Order Number
              </Typography>
              <Typography variant="h6" fontWeight="bold">
                #{orderData.shopifyOrderNumber}
              </Typography>
            </Box>

            {effectiveLineItem && (
              <Box>
                <Typography variant="body1" sx={{ opacity: 0.8, mb: 0.5 }}>
                  Item Number
                </Typography>
                <Typography variant="h6" fontWeight="bold">
                  {effectiveLineItem.itemNumber}
                </Typography>
              </Box>
            )}

            <Box sx={{ ml: 'auto', display: 'flex', gap: 1, alignItems: 'center' }}>
              {rejectedCount > 0 && (
                <Chip
                  icon={<Info />}
                  label={`${rejectedCount} image${rejectedCount > 1 ? 's' : ''} to replace`}
                  sx={{
                    bgcolor: 'rgba(255, 255, 255, 0.2)',
                    color: 'white',
                    fontWeight: 600,
                    '& .MuiChip-icon': { color: 'white' },
                  }}
                />
              )}

              {uploadedCount > 0 && (
                <Chip
                  icon={<Schedule />}
                  label={`${uploadedCount} ready to submit`}
                  sx={{
                    bgcolor: 'rgba(76, 175, 80, 0.8)',
                    color: 'white',
                    fontWeight: 600,
                    '& .MuiChip-icon': { color: 'white' },
                  }}
                />
              )}
            </Box>
          </Box>
        </CardContent>
      </Card>

      {/* Progress Indicator */}
      {(isSubmitting || uploading) && (
        <Card
          elevation={2}
          sx={{
            mb: 3,
            borderRadius: 2,
            overflow: 'hidden',
          }}
        >
          <LinearProgress color={isSubmitting ? 'success' : 'primary'} sx={{ height: 6 }} />
          <CardContent sx={{ py: 2 }}>
            <Typography variant="body2" color="text.secondary" textAlign="center" fontWeight={500}>
              {isSubmitting
                ? 'Submitting your images... Please wait.'
                : 'Uploading image... Please wait.'}
            </Typography>
          </CardContent>
        </Card>
      )}
    </>
  );
};

export default RequestHeader;
