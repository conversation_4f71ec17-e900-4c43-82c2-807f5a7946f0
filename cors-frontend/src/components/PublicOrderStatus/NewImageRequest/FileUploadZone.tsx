'use client';

import React, { useRef, useState } from 'react';
import { Paper, Typography, LinearProgress, Box, Fade, Zoom } from '@mui/material';
import { CloudUpload, Image as ImageIcon } from '@mui/icons-material';

interface FileUploadZoneProps {
  onFileSelect: (file: File) => void;
  uploading?: boolean;
  disabled?: boolean;
  acceptedTypes?: string;
  maxSizeMB?: number;
  id: string;
}

const FileUploadZone: React.FC<FileUploadZoneProps> = ({
  onFileSelect,
  uploading = false,
  disabled = false,
  acceptedTypes = 'image/*',
  maxSizeMB = 10,
  id,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragOver, setIsDragOver] = useState(false);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onFileSelect(file);
    }
    // Reset input value to allow selecting the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    if (!disabled && !uploading) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);

    if (disabled || uploading) return;

    const files = event.dataTransfer.files;
    if (files.length > 0) {
      onFileSelect(files[0]);
    }
  };

  const handleClick = () => {
    if (!disabled && !uploading) {
      fileInputRef.current?.click();
    }
  };

  const isInteractive = !disabled && !uploading;

  return (
    <Box sx={{ position: 'relative' }}>
      <input
        ref={fileInputRef}
        id={id}
        type="file"
        accept={acceptedTypes}
        onChange={handleFileChange}
        style={{ display: 'none' }}
        disabled={disabled || uploading}
      />

      <Paper
        variant="outlined"
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleClick}
        sx={{
          p: 4,
          textAlign: 'center',
          borderStyle: 'dashed',
          borderWidth: 2,
          borderRadius: 3,
          cursor: isInteractive ? 'pointer' : 'not-allowed',
          opacity: disabled || uploading ? 0.6 : 1,
          transition: 'all 0.3s ease',
          position: 'relative',
          overflow: 'hidden',
          minHeight: 200,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          borderColor: isDragOver ? 'primary.main' : uploading ? 'warning.main' : 'grey.300',
          bgcolor: isDragOver ? 'primary.50' : uploading ? 'warning.50' : 'background.paper',
          '&:hover': isInteractive
            ? {
                borderColor: 'primary.main',
                bgcolor: 'primary.50',
                transform: 'translateY(-2px)',
                boxShadow: 4,
              }
            : undefined,
        }}
      >
        {/* Upload Icon with Animation */}
        <Zoom in={!uploading} timeout={300}>
          <Box>
            <CloudUpload
              sx={{
                fontSize: 64,
                color: isDragOver ? 'primary.main' : 'grey.400',
                mb: 2,
                transition: 'all 0.3s ease',
              }}
            />
          </Box>
        </Zoom>

        {/* Loading State */}
        {uploading && (
          <Fade in={uploading}>
            <Box sx={{ mb: 2 }}>
              <ImageIcon
                sx={{
                  fontSize: 64,
                  color: 'warning.main',
                  animation: 'pulse 1.5s ease-in-out infinite',
                  '@keyframes pulse': {
                    '0%': { opacity: 1 },
                    '50%': { opacity: 0.5 },
                    '100%': { opacity: 1 },
                  },
                }}
              />
            </Box>
          </Fade>
        )}

        {/* Text Content */}
        <Box>
          <Typography
            variant="h6"
            gutterBottom
            sx={{
              fontWeight: 600,
              color: uploading ? 'warning.main' : isDragOver ? 'primary.main' : 'text.primary',
            }}
          >
            {uploading
              ? 'Uploading your image...'
              : isDragOver
                ? 'Drop your image here'
                : 'Drop your image here'}
          </Typography>

          {!uploading && (
            <>
              <Typography variant="body1" color="text.secondary" gutterBottom sx={{ mb: 2 }}>
                or click to browse files
              </Typography>

              <Box
                sx={{
                  p: 2,
                  borderRadius: 2,
                  border: '1px solid',
                  borderColor: 'grey.200',
                }}
              >
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  <strong>Supported formats:</strong> JPG, PNG,
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  <strong>Maximum size:</strong> {maxSizeMB}MB
                </Typography>
              </Box>
            </>
          )}
        </Box>

        {/* Progress Bar */}
        {uploading && (
          <Box sx={{ width: '100%', mt: 3 }}>
            <LinearProgress
              color="warning"
              sx={{
                height: 6,
                borderRadius: 3,
                bgcolor: 'warning.100',
              }}
            />
            <Typography
              variant="caption"
              color="warning.main"
              sx={{ mt: 1, display: 'block', fontWeight: 500 }}
            >
              Please wait while we process your image...
            </Typography>
          </Box>
        )}

        {/* Drag Overlay */}
        {isDragOver && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              bgcolor: 'rgba(25, 118, 210, 0.1)',
              border: '3px dashed',
              borderColor: 'primary.main',
              borderRadius: 3,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 1,
            }}
          >
            <Typography variant="h5" color="primary.main" sx={{ fontWeight: 700 }}>
              Drop to upload
            </Typography>
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default FileUploadZone;
