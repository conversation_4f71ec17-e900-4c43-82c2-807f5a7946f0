import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export type FilterCondition = {
  attribute: string;
  operator: string;
  value: string | number | boolean | (string | number | boolean)[];
};

export type FilterGroup = FilterCondition[];
export type Filters = FilterGroup[];

export interface OrdersType {
  unflagged: {
    data: any[];
    count: number;
  };
  flagged: {
    data: any[];
    count: number;
  };
  loading: boolean;
  error: string | null;
  filters: Filters;
}

const initialState: OrdersType = {
  unflagged: { data: [], count: 0 },
  flagged: { data: [], count: 0 },
  loading: false,
  error: null,
  filters: [],
};

const ordersSlice = createSlice({
  name: 'orders',
  initialState,
  reducers: {
    setOrders: (state, action: PayloadAction<any>) => {
      state.unflagged = action.payload.unflagged || { data: [], count: 0 };
      state.flagged = action.payload.flagged || { data: [], count: 0 };
    },
    setOrdersFilters: (state, action: PayloadAction<Filters>) => {
      state.filters = action.payload;
    },

    clearFilters: state => {
      state.filters = [];
      state.unflagged = { data: [], count: 0 };
      state.flagged = { data: [], count: 0 };
      state.loading = true;
    },
  },
});

export const { setOrders, setOrdersFilters, clearFilters } = ordersSlice.actions;
export default ordersSlice.reducer;
