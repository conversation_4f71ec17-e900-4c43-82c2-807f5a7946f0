import axios from "axios";

export function handleApiError(error: unknown, fallbackMessage = 'An unexpected error occurred'): Error {
  if (error && typeof error === 'object') {
    if ('response' in error && error.response && typeof error.response === 'object') {
      const response = (error as any).response;
      if (response.data && typeof response.data === 'object' && 'message' in response.data) {
        return new Error(response.data.message || fallbackMessage);
      }
      if (typeof response.statusText === 'string' && response.statusText) {
        return new Error(response.statusText);
      }
    }
    if ('message' in error && typeof (error as any).message === 'string') {
      return new Error((error as any).message);
    }
  }
  return new Error(fallbackMessage);
} 


export function handlePublicApiError(error: any, context: string): never {
  if (axios.isAxiosError(error)) {
    if (error.response?.status === 401 || error.response?.status === 403) {
      throw new Error('Invalid access. Please use the link from your shopify order tracking.');
    }
    if (error.response?.status === 404) {
      throw new Error(context.includes('rejected image')
        ? 'No rejected image found for this order'
        : 'Order not found. Please check your order number and email.');
    }
    if (error.response?.status && error.response.status >= 500) {
      throw new Error('Server error. Please try again later.');
    }
    throw new Error(error.response?.data?.message || `Failed to ${context.toLowerCase()}`);
  }
  throw new Error(`Unable to ${context.toLowerCase()}. Please check your connection and try again.`);
}
