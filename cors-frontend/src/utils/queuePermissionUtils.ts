import { Actions, ActionsTarget } from '@/libs/casl/ability';

export interface QueuePermission {
  action: Actions;
  target: ActionsTarget;
}

/**
 * Queue mapping configuration
 */
const QUEUE_MAPPINGS = {
  'Crop Review': {
    view: { action: Actions.ViewCropReviewQueue, target: ActionsTarget.CROP_REVIEW_QUEUE },
    start: { action: Actions.StartStopCropReview, target: ActionsTarget.CROP_REVIEW_QUEUE },
  },
  'Crop Needed': {
    view: { action: Actions.ViewCropNeededQueue, target: ActionsTarget.CROP_NEEDED_QUEUE },
    start: { action: Actions.StartStopCropNeeded, target: ActionsTarget.CROP_NEEDED_QUEUE },
  },
  'Template Placement': {
    view: {
      action: Actions.ViewTemplatePlacementQueue,
      target: ActionsTarget.TEMPLATE_PLACEMENT_QUEUE,
    },
    start: {
      action: Actions.StartStopTemplatePlacement,
      target: ActionsTarget.TEMPLATE_PLACEMENT_QUEUE,
    },
  },
  'Ready For Artwork': {
    view: { action: Actions.ViewArtworkReadyQueue, target: ActionsTarget.ARTWORK_READY_QUEUE },
    start: { action: Actions.StartStopArtworkReady, target: ActionsTarget.ARTWORK_READY_QUEUE },
  },
  'Artwork Revision': {
    view: {
      action: Actions.ViewArtworkRevisionQueue,
      target: ActionsTarget.ARTWORK_REVISION_QUEUE,
    },
    start: {
      action: Actions.StartStopArtworkRevision,
      target: ActionsTarget.ARTWORK_REVISION_QUEUE,
    },
  },
} as const;

/**
 * Get the View permission for a queue by name
 */
export const getViewPermissionForQueue = (queueName: string): QueuePermission | null => {
  return QUEUE_MAPPINGS[queueName as keyof typeof QUEUE_MAPPINGS]?.view || null;
};

/**
 * Get the Start permission for a queue by name
 */
export const getStartPermissionForQueue = (queueName: string): QueuePermission | null => {
  return QUEUE_MAPPINGS[queueName as keyof typeof QUEUE_MAPPINGS]?.start || null;
};

/**
 * Get the Stop permission for a queue by name
 */
export const getStopPermissionForQueue = (queueName: string): QueuePermission | null => {
  return QUEUE_MAPPINGS[queueName as keyof typeof QUEUE_MAPPINGS]?.start || null;
};

/**
 * Queue type mapping configuration (for role form)
 */
const QUEUE_TYPE_MAPPINGS = {
  'Crop Review': {
    view: Actions.ViewCropReviewQueue,
    start: Actions.StartStopCropReview,
  },
  'Crop Needed': {
    view: Actions.ViewCropNeededQueue,
    start: Actions.StartStopCropNeeded,
  },
  'Template Placement': {
    view: Actions.ViewTemplatePlacementQueue,
    start: Actions.StartStopTemplatePlacement,
  },
  'Artwork Ready': {
    view: Actions.ViewArtworkReadyQueue,
    start: Actions.StartStopArtworkReady,
  },
  'Artwork Revision': {
    view: Actions.ViewArtworkRevisionQueue,
    start: Actions.StartStopArtworkRevision,
  },
  'Ready for Artwork': {
    view: Actions.ViewArtworkReadyQueue,
    start: Actions.StartStopArtworkReady,
  },
} as const;

/**
 * Get the View action for a queue type (for role form)
 */
export const getViewActionForQueue = (queueType: string): Actions | null => {
  return QUEUE_TYPE_MAPPINGS[queueType as keyof typeof QUEUE_TYPE_MAPPINGS]?.view || null;
};

/**
 * Get the Start action for a queue type (for role form)
 */
export const getStartActionForQueue = (queueType: string): Actions | null => {
  return QUEUE_TYPE_MAPPINGS[queueType as keyof typeof QUEUE_TYPE_MAPPINGS]?.start || null;
};

/**
 * Get the Stop action for a queue type (for role form)
 */
export const getStopActionForQueue = (queueType: string): Actions | null => {
  return QUEUE_TYPE_MAPPINGS[queueType as keyof typeof QUEUE_TYPE_MAPPINGS]?.start || null;
};

/**
 * List of all queue types
 */
export const QUEUE_TYPES = [
  'Crop Review',
  'Crop Needed',
  'Template Placement',
  'Artwork Ready',
  'Artwork Revision',
  'Ready for Artwork',
];
