import { FormType } from '@/types/public-order-status.types';

/**
 * Utility function to determine form type from URL parameters
 * @param params - URL search parameters
 * @returns FormType or undefined if no form type is specified
 */
export function getFormTypeFromParams(params: {
  [key: string]: string | string[] | undefined;
}): FormType | undefined {
  if (params.newImageRequest !== undefined) {
    return 'newImageRequest';
  } else if (params.customerContactNeeded !== undefined) {
    return 'customerContactNeeded';
  } else if (params.customerApproval !== undefined) {
    return 'customerApproval';
  }
  return undefined;
}

/**
 * Common error handler for order status pages
 * @param error - Error object
 * @returns User-friendly error message
 */
export function handleOrderStatusError(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  return 'Failed to fetch order status';
}
