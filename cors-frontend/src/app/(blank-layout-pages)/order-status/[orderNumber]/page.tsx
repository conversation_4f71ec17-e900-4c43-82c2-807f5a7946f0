import React from 'react';
import { Metadata } from 'next';
import PublicOrderStatusPage from '@/views/PublicOrderStatus';
import { fetchPublicOrderStatus } from '@/actions/public-order-status';
import { PublicOrderStatus } from '@/types/public-order-status.types';
import { handleOrderStatusError } from '@/utils/order-status.utils';

export const metadata: Metadata = {
  title: 'Order Status - Track Your Order',
  description: 'Check the status of your order and track its progress.',
};

interface OrderStatusWithNumberPageProps {
  params: Promise<{ orderNumber: string }>;
  searchParams: Promise<{ [key: string]: string | undefined }>;
}

const OrderStatusWithNumberPage: React.FC<OrderStatusWithNumberPageProps> = async ({
  params,
  searchParams,
}) => {
  const resolvedParams = await params;
  const resolvedSearchParams = await searchParams;
  let orderData: PublicOrderStatus | null = null;
  let error: string | null = null;

  try {
    orderData = await fetchPublicOrderStatus(
      resolvedParams.orderNumber,
      resolvedSearchParams.customerEmail || '',
    );
  } catch (err) {
    error = handleOrderStatusError(err);
  }

  return (
    <PublicOrderStatusPage
      initialOrderData={orderData}
      initialError={error}
      shopifyOrderNumber={resolvedParams.orderNumber}
      customerEmail={resolvedSearchParams.customerEmail || ''}
    />
  );
};

export default OrderStatusWithNumberPage;
